
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Notaflow</title>
    <meta name="description" content="Notaflow - Outil de recherche cadastrale par ParAito" />
    <meta name="author" content="ParAito" />
    <meta property="og:image" content="/og-image.png" />
    <link rel="icon" href="/lovable-uploads/0ff94384-bf77-4e7d-a1d4-9c9dbdcc447d.png" type="image/png" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap">
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <!-- <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script> -->
    <script type="module" src="/src/main.tsx"></script>
    <script>
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(function(registrations) {
          for (let registration of registrations) {
            registration.unregister();
          }
        }).catch(function(err) {
          console.log('Service Worker registration failed: ', err);
        });
      }
    </script>
  </body>
</html>
