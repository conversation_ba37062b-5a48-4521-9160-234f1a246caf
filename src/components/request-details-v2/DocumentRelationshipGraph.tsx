import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Maximize2, 
  Minimize2, 
  Download, 
  RefreshCw,
  ZoomIn,
  ZoomOut,
  Move
} from 'lucide-react';
import mermaid from 'mermaid/dist/mermaid.esm.min.mjs';
import { cn } from '@/lib/utils';

interface DocumentNode {
  id: string;
  type: 'index' | 'acte';
  name: string;
  status: string;
  children?: DocumentNode[];
}

interface DocumentRelationshipGraphProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  className?: string;
}

const DocumentRelationshipGraph: React.FC<DocumentRelationshipGraphProps> = ({
  indexEntries,
  actesByIndex,
  className
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(1);
  const graphRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize mermaid
  useEffect(() => {
    mermaid.initialize({
      startOnLoad: true,
      theme: 'default',
      themeVariables: {
        primaryColor: '#3b82f6',
        primaryTextColor: '#fff',
        primaryBorderColor: '#2563eb',
        lineColor: '#6b7280',
        secondaryColor: '#8b5cf6',
        tertiaryColor: '#10b981',
        background: '#f9fafb',
        mainBkg: '#3b82f6',
        secondBkg: '#8b5cf6',
        tertiaryBkg: '#10b981',
        darkMode: false,
      },
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis',
        rankSpacing: 80,
        nodeSpacing: 80,
        padding: 20
      }
    });
  }, []);

  // Generate mermaid diagram definition
  const generateMermaidDiagram = () => {
    let diagram = `graph TD\n`;
    diagram += `  Request["📋 Demande"]:::request\n`;
    
    // Add index nodes
    indexEntries.forEach((index, idx) => {
      const indexId = `Index${idx}`;
      const status = index.status?.toLowerCase() || '';
      const statusClass = status.includes('completed') ? 'completed' : 
                         status.includes('error') ? 'error' : 'inprogress';
      
      diagram += `  ${indexId}["📁 Index ${idx + 1}<br/>${index.lot_number}<br/><i>${index.status || 'En cours'}</i>"]:::${statusClass}\n`;
      diagram += `  Request --> ${indexId}\n`;
      
      // Add acte nodes for this index
      const actes = actesByIndex[index.id] || [];
      actes.forEach((acte, acteIdx) => {
        const acteId = `Acte${idx}_${acteIdx}`;
        const acteStatus = acte.status?.toLowerCase() || '';
        const acteStatusClass = acteStatus.includes('completed') ? 'completed' : 
                               acteStatus.includes('not available') ? 'error' : 'inprogress';
        
        const nature = acte.acte_nature || 'Inscription';
        const pubNumber = acte.acte_publication_number || `#${acteIdx + 1}`;
        
        diagram += `  ${acteId}["📄 ${nature}<br/>${pubNumber}<br/><i>${acte.status || 'En cours'}</i>"]:::${acteStatusClass}\n`;
        diagram += `  ${indexId} --> ${acteId}\n`;
        
        // If this acte has radiated status
        if (acte.is_radiated) {
          diagram += `  ${acteId} -.- Radiated${idx}_${acteIdx}["❌ Radié"]:::radiated\n`;
        }
      });
    });
    
    // Add styles
    diagram += `\n  classDef request fill:#f59e0b,stroke:#d97706,stroke-width:3px,color:#fff\n`;
    diagram += `  classDef completed fill:#10b981,stroke:#059669,stroke-width:2px,color:#fff\n`;
    diagram += `  classDef inprogress fill:#3b82f6,stroke:#2563eb,stroke-width:2px,color:#fff\n`;
    diagram += `  classDef error fill:#ef4444,stroke:#dc2626,stroke-width:2px,color:#fff\n`;
    diagram += `  classDef radiated fill:#fee2e2,stroke:#ef4444,stroke-width:1px,stroke-dasharray: 5 5,color:#991b1b\n`;
    
    return diagram;
  };

  // Render the diagram
  const renderDiagram = async () => {
    if (!graphRef.current) return;
    
    const diagram = generateMermaidDiagram();
    graphRef.current.innerHTML = ''; // Clear previous content
    
    try {
      const { svg } = await mermaid.render('mermaid-graph', diagram);
      graphRef.current.innerHTML = svg;
    } catch (error) {
      console.error('Error rendering mermaid diagram:', error);
      graphRef.current.innerHTML = '<p class="text-red-500">Erreur lors du rendu du graphique</p>';
    }
  };

  // Render diagram on mount and when data changes
  useEffect(() => {
    renderDiagram();
  }, [indexEntries, actesByIndex]);

  // Handle zoom
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 2));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.5));
  const handleResetZoom = () => setZoom(1);

  // Handle fullscreen
  const toggleFullscreen = () => {
    if (!document.fullscreenElement && containerRef.current) {
      containerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else if (document.exitFullscreen) {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle download
  const handleDownload = () => {
    const svg = graphRef.current?.querySelector('svg');
    if (!svg) return;
    
    const svgData = new XMLSerializer().serializeToString(svg);
    const blob = new Blob([svgData], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'document-relationships.svg';
    link.click();
    
    URL.revokeObjectURL(url);
  };

  return (
    <Card className={cn("overflow-hidden", className)} ref={containerRef}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Move className="h-5 w-5" />
          Relations entre documents
        </CardTitle>
        
        <div className="flex items-center gap-2">
          {/* Zoom controls */}
          <div className="flex items-center gap-1 border rounded-md">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleZoomOut}
              className="h-8 w-8"
              disabled={zoom <= 0.5}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <span className="px-2 text-sm font-medium">{Math.round(zoom * 100)}%</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleZoomIn}
              className="h-8 w-8"
              disabled={zoom >= 2}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleResetZoom}
            className="h-8 w-8"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleDownload}
            className="h-8 w-8"
          >
            <Download className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={toggleFullscreen}
            className="h-8 w-8"
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="relative overflow-auto" style={{ maxHeight: isFullscreen ? '100vh' : '600px' }}>
          <div 
            ref={graphRef}
            className="p-8 flex items-center justify-center min-h-[400px]"
            style={{ transform: `scale(${zoom})`, transformOrigin: 'center top' }}
          />
        </div>
        
        {/* Legend */}
        <div className="border-t p-4 bg-muted/50">
          <h4 className="text-sm font-semibold mb-2">Légende</h4>
          <div className="flex flex-wrap gap-3 text-xs">
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-amber-500 rounded" />
              <span>Demande</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-blue-500 rounded" />
              <span>En cours</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-green-500 rounded" />
              <span>Complété</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-red-500 rounded" />
              <span>Erreur / Non disponible</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 border-2 border-red-500 border-dashed rounded" />
              <span>Radié</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DocumentRelationshipGraph;
