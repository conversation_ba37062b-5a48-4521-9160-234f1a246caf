import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  Building2,
  Users,
  FileWarning,
  Lightbulb,
  ChevronRight,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AnalysisProvider, useAnalysis } from './AnalysisContext';
import { parseAnalysisContent } from './AnalysisItemParser';
import AnalysisItem from './AnalysisItem';

interface AnalysisSectionProps {
  servitudes: string | null;
  regimesMatrimoniaux: string | null;
  erreurs: string | null;
  autresConsiderations: string | null;
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
}

const AnalysisSectionContent: React.FC<AnalysisSectionProps> = ({
  servitudes,
  regimesMatrimoniaux,
  erreurs,
  autresConsiderations,
  indexEntries,
  actesByIndex
}) => {
  const { expandedSections, setExpandedSections, priorityFilter, setPriorityFilter } = useAnalysis();

  // Helper to find document by ID
  const findDocumentById = (id: string) => {
    // Search in index entries
    const foundIndex = indexEntries.find(entry => entry.id === id || entry.lot_number === id || entry.doc_number?.toString() === id);
    if (foundIndex) return { type: 'index', data: foundIndex };

    // Search in actes
    for (const indexId in actesByIndex) {
      const foundActe = actesByIndex[indexId].find(acte => acte.id === id || acte.acte_publication_number === id);
      if (foundActe) return { type: 'acte', data: foundActe };
    }
    return null;
  };

  // Parse content into structured items
  const parsedSections = useMemo(() => {
    return [
      {
        id: 'servitudes',
        title: 'Servitudes',
        icon: Building2,
        items: parseAnalysisContent(servitudes),
        color: 'blue',
      },
      {
        id: 'regimes',
        title: 'Régimes matrimoniaux',
        icon: Users,
        items: parseAnalysisContent(regimesMatrimoniaux),
        color: 'purple',
      },
      {
        id: 'erreurs',
        title: 'Erreurs et anomalies',
        icon: FileWarning,
        items: parseAnalysisContent(erreurs),
        color: 'red',
      },
      {
        id: 'autres',
        title: 'Autres considérations',
        icon: Lightbulb,
        items: parseAnalysisContent(autresConsiderations),
        color: 'yellow',
      }
    ];
  }, [servitudes, regimesMatrimoniaux, erreurs, autresConsiderations]);

  // Filter items by priority
  const filteredSections = useMemo(() => {
    if (priorityFilter === 'all') return parsedSections;
    
    return parsedSections.map(section => ({
      ...section,
      items: section.items.filter(item => item.priority === priorityFilter)
    }));
  }, [parsedSections, priorityFilter]);

  // Calculate statistics
  const stats = useMemo(() => {
    const allItems = parsedSections.flatMap(section => section.items);
    const highPriorityCount = allItems.filter(item => item.priority === 'high').length;
    const mediumPriorityCount = allItems.filter(item => item.priority === 'medium').length;
    const lowPriorityCount = allItems.filter(item => item.priority === 'low').length;
    
    return {
      total: allItems.length,
      highPriority: highPriorityCount,
      mediumPriority: mediumPriorityCount,
      lowPriority: lowPriorityCount,
      hasContent: allItems.length > 0
    };
  }, [parsedSections]);

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return 'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300';
      case 'purple':
        return 'border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300';
      case 'red':
        return 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300';
      case 'yellow':
        return 'border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-300';
    }
  };

  if (!stats.hasContent) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Aucun problème détecté</h3>
          <p className="text-muted-foreground">
            L'analyse n'a révélé aucune servitude, erreur ou considération particulière.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Alert */}
      {stats.highPriority > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Attention requise</AlertTitle>
          <AlertDescription>
            {stats.highPriority} élément{stats.highPriority > 1 ? 's' : ''} de haute priorité 
            nécessite{stats.highPriority > 1 ? 'nt' : ''} votre attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Priority Filter */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Filtrer par priorité</h3>
        <div className="flex items-center gap-2">
          <Button 
            variant={priorityFilter === 'all' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setPriorityFilter('all')}
          >
            Tous ({stats.total})
          </Button>
          <Button 
            variant={priorityFilter === 'high' ? 'destructive' : 'outline'} 
            size="sm"
            onClick={() => setPriorityFilter('high')}
            className={priorityFilter !== 'high' ? 'text-red-600 border-red-200' : ''}
          >
            Haute ({stats.highPriority})
          </Button>
          <Button 
            variant={priorityFilter === 'medium' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setPriorityFilter('medium')}
            className={priorityFilter !== 'medium' ? 'text-amber-600 border-amber-200' : ''}
          >
            Moyenne ({stats.mediumPriority})
          </Button>
          <Button 
            variant={priorityFilter === 'low' ? 'secondary' : 'outline'} 
            size="sm"
            onClick={() => setPriorityFilter('low')}
          >
            Basse ({stats.lowPriority})
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total des éléments</p>
              <p className="text-2xl font-bold">{stats.total}</p>
            </div>
            <Info className="h-8 w-8 text-muted-foreground/20" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Haute priorité</p>
              <p className="text-2xl font-bold text-red-600">{stats.highPriority}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-600/20" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Catégories actives</p>
              <p className="text-2xl font-bold">{filteredSections.filter(s => s.items.length > 0).length}</p>
            </div>
            <Filter className="h-8 w-8 text-green-600/20" />
          </div>
        </Card>
      </div>

      {/* Detailed Analysis */}
      <Accordion 
        type="multiple" 
        value={expandedSections}
        onValueChange={setExpandedSections}
        className="space-y-4"
      >
        {filteredSections.map((section) => {
          if (section.items.length === 0) return null;
          
          const Icon = section.icon;
          const itemCount = section.items.length;
          const highPriorityCount = section.items.filter(item => item.priority === 'high').length;
          const hasPriority = priorityFilter !== 'all';
          
          return (
            <AccordionItem 
              key={section.id} 
              value={section.id}
              className="border-none"
            >
              <Card className={cn(
                "overflow-hidden border-2 transition-all",
                expandedSections.includes(section.id) && getColorClasses(section.color)
              )}>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "p-2 rounded-lg",
                        getColorClasses(section.color)
                      )}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="text-left">
                        <h3 className="font-semibold">{section.title}</h3>
                        <p className="text-sm text-muted-foreground">
                          {itemCount} élément{itemCount > 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getPriorityBadge(section.priority)}
                      <ChevronRight className="h-4 w-4 transition-transform" />
                    </div>
                  </div>
                </AccordionTrigger>
                
                <AccordionContent>
                  <CardContent className="px-6 pb-6">
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <ReactMarkdown
                        components={{
                          // Custom link renderer to add icon
                          a: ({ node, ...props }) => (
                            <a 
                              {...props} 
                              className="inline-flex items-center gap-1 text-primary hover:underline"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {props.children}
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          ),
                          // Custom list item renderer
                          li: ({ node, ...props }) => (
                            <li className="mb-2" {...props} />
                          )
                        }}
                      >
                        {section.content}
                      </ReactMarkdown>
                    </div>
                    
                    {/* Related documents section */}
                    <div className="mt-6 pt-4 border-t">
                      <h4 className="text-sm font-semibold mb-2">Documents liés</h4>
                      <div className="flex gap-2 flex-wrap">
                        {extractDocumentIds(section.content).map(docId => {
                          const doc = findDocumentById(docId);
                          if (doc) {
                            const linkPath = doc.type === 'index' ? `/requests-v2/${doc.data.request_id}?tab=documents&index=${doc.data.id}` : `/requests-v2/${doc.data.request_id}?tab=documents&acte=${doc.data.id}`;
                            return (
                              <Link to={linkPath} key={docId}>
                                <Badge variant="outline" className="text-xs cursor-pointer hover:bg-muted/70">
                                  {doc.type === 'index' ? `Index ${doc.data.lot_number}` : `Acte ${doc.data.acte_publication_number}`}
                                </Badge>
                              </Link>
                            );
                          }
                          return null;
                        })}
                        {extractDocumentIds(section.content).length === 0 && (
                          <p className="text-muted-foreground text-xs">Aucun document lié trouvé.</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
};

export default AnalysisSection;
