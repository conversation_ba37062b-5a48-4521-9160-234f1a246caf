import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  LayoutDashboard, 
  FileText, 
  BarChart3, 
  Clock, 
  MessageSquare,
  Filter,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface RequestDetailsTabsProps {
  requestId: string;
  children?: {
    overview: React.ReactNode;
    documents: React.ReactNode;
    analysis: React.ReactNode;
    timeline: React.ReactNode;
    chat: React.ReactNode;
  };
}

const RequestDetailsTabs: React.FC<RequestDetailsTabsProps> = ({ requestId, children }) => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 z-10 pb-4">
          <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:inline-flex">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <LayoutDashboard className="h-4 w-4" />
              <span className="hidden sm:inline">Vue d'ensemble</span>
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Documents</span>
            </TabsTrigger>
            <TabsTrigger value="analysis" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span className="hidden sm:inline">Analyse</span>
            </TabsTrigger>
            <TabsTrigger value="timeline" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span className="hidden sm:inline">Chronologie</span>
            </TabsTrigger>
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Discussion</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="mt-6 space-y-4">
          {children?.overview || <div>Overview content placeholder</div>}
        </TabsContent>

        <TabsContent value="documents" className="mt-6 space-y-4">
          {children?.documents || <div>Documents content placeholder</div>}
        </TabsContent>

        <TabsContent value="analysis" className="mt-6 space-y-4">
          {children?.analysis || <div>Analysis content placeholder</div>}
        </TabsContent>

        <TabsContent value="timeline" className="mt-6 space-y-4">
          {children?.timeline || <div>Timeline content placeholder</div>}
        </TabsContent>

        <TabsContent value="chat" className="mt-6 space-y-4">
          {children?.chat || <div>Chat content placeholder</div>}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RequestDetailsTabs;
