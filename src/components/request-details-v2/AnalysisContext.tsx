import React, { createContext, useContext, useState, ReactNode } from 'react';

type PriorityFilter = 'all' | 'high' | 'medium' | 'low';

interface AnalysisContextType {
  expandedSections: string[];
  setExpandedSections: (sections: string[]) => void;
  priorityFilter: PriorityFilter;
  setPriorityFilter: (filter: PriorityFilter) => void;
}

const AnalysisContext = createContext<AnalysisContextType | undefined>(undefined);

export const AnalysisProvider: React.FC<{children: ReactNode}> = ({ children }) => {
  const [expandedSections, setExpandedSections] = useState<string[]>(['servitudes']);
  const [priorityFilter, setPriorityFilter] = useState<PriorityFilter>('all');

  return (
    <AnalysisContext.Provider value={{
      expandedSections,
      setExpandedSections,
      priorityFilter,
      setPriorityFilter
    }}>
      {children}
    </AnalysisContext.Provider>
  );
};

export const useAnalysis = () => {
  const context = useContext(AnalysisContext);
  if (context === undefined) {
    throw new Error('useAnalysis must be used within an AnalysisProvider');
  }
  return context;
};