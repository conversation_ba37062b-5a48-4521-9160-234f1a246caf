interface AnalysisItem {
  id: string;
  content: string;
  priority: 'high' | 'medium' | 'low';
  relatedDocIds: string[];
}

export const parseAnalysisContent = (content: string | null): AnalysisItem[] => {
  if (!content) return [];
  
  // Split content by bullet points or numbered items
  const itemRegex = /(?:^|\n)(?:[-*]|\d+\.)\s+(.*?)(?=(?:\n[-*]|\n\d+\.|\n\n|$))/gs;
  const matches = [...content.matchAll(itemRegex)];
  
  if (matches.length === 0 && content.trim()) {
    // If no bullet points found but content exists, treat as single item
    return [{
      id: `item-${Date.now()}`,
      content: content.trim(),
      priority: determinePriority(content),
      relatedDocIds: extractDocumentIds(content)
    }];
  }
  
  return matches.map((match, index) => {
    const itemContent = match[1].trim();
    return {
      id: `item-${index}`,
      content: itemContent,
      priority: determinePriority(itemContent),
      relatedDocIds: extractDocumentIds(itemContent)
    };
  });
};

const determinePriority = (content: string): 'high' | 'medium' | 'low' => {
  const lowerContent = content.toLowerCase();
  
  if (lowerContent.includes('urgent') || 
      lowerContent.includes('important') ||
      lowerContent.includes('attention')) {
    return 'high';
  }
  
  if (lowerContent.includes('vérifier') || 
      lowerContent.includes('confirmer') ||
      lowerContent.includes('noter')) {
    return 'medium';
  }
  
  return 'low';
};

export const extractDocumentIds = (content: string): string[] => {
  const regex = /(index|acte) #(\w+)/gi;
  const matches = [...content.matchAll(regex)];
  return matches.map(match => match[2]);
};