import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { AlertTriangle, Info, FileText } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface AnalysisItemProps {
  content: string;
  priority: 'high' | 'medium' | 'low';
  relatedDocIds: string[];
  findDocumentById: (id: string) => any;
  colorClass: string;
}

const AnalysisItem: React.FC<AnalysisItemProps> = ({
  content,
  priority,
  relatedDocIds,
  findDocumentById,
  colorClass
}) => {
  const PriorityIcon = priority === 'high' 
    ? AlertTriangle 
    : priority === 'medium' 
      ? Info 
      : FileText;
  
  const priorityClasses = {
    high: 'border-red-500 bg-red-50 dark:bg-red-950/30',
    medium: 'border-amber-500 bg-amber-50 dark:bg-amber-950/30',
    low: 'border-slate-200 bg-slate-50 dark:bg-slate-900/30'
  };

  return (
    <Card className={cn(
      'mb-3 border-l-4 p-4 transition-all',
      priorityClasses[priority],
      colorClass
    )}>
      <div className="flex gap-3">
        <div className="flex-shrink-0 mt-1">
          <PriorityIcon className={cn(
            'h-5 w-5',
            priority === 'high' ? 'text-red-500' : 
            priority === 'medium' ? 'text-amber-500' : 'text-slate-500'
          )} />
        </div>
        <div className="flex-grow space-y-2">
          <div className="prose prose-sm dark:prose-invert max-w-none">
            <ReactMarkdown>{content}</ReactMarkdown>
          </div>
          
          {relatedDocIds.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2 pt-2 border-t border-border/40">
              {relatedDocIds.map(docId => {
                const doc = findDocumentById(docId);
                if (doc) {
                  const linkPath = doc.type === 'index' 
                    ? `/requests-v2/${doc.data.request_id}?tab=documents&index=${doc.data.id}` 
                    : `/requests-v2/${doc.data.request_id}?tab=documents&acte=${doc.data.id}`;
                  return (
                    <Link to={linkPath} key={docId}>
                      <Badge variant="outline" className="text-xs cursor-pointer hover:bg-muted/70 flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        {doc.type === 'index' ? `Index ${doc.data.lot_number}` : `Acte ${doc.data.acte_publication_number}`}
                      </Badge>
                    </Link>
                  );
                }
                return null;
              })}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default AnalysisItem;