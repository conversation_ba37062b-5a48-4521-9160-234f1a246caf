# Request Details Page Redesign - Demo Guide

## How to View the New Design

### 1. Install Required Dependencies

First, you need to install the Mermaid library for the document relationship graph:

```bash
npm install mermaid
# or
yarn add mermaid
```

### 2. Create a Demo Page

Create a new file `src/pages/RequestDetailsDemo.tsx` to see all the new components in action:

```tsx
import React from 'react';
import { useParams } from 'react-router-dom';
import RequestDetailsTabs from '@/components/request-details-v2/RequestDetailsTabs';
import MetricsDashboard from '@/components/request-details-v2/MetricsDashboard';
import EnhancedIndexCard from '@/components/request-details-v2/EnhancedIndexCard';
import FilterBar from '@/components/request-details-v2/FilterBar';
import AnalysisSection from '@/components/request-details-v2/AnalysisSection';
import DocumentRelationshipGraph from '@/components/request-details-v2/DocumentRelationshipGraph';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>T<PERSON>le, CardContent } from '@/components/ui/card';

const RequestDetailsDemo = () => {
  // Sample data for demonstration
  const sampleRequest = {
    id: 'demo-123',
    seller_name: 'Jean Dupont',
    seller_address: '123 Rue de la Paix, Montréal, QC',
    status: 'Phase 2',
    created_at: '2025-01-15',
    servitudes: '- Servitude de passage en faveur du lot voisin\n- Servitude d\'Hydro-Québec pour ligne électrique',
    regimes_matrimoniaux: '- Régime de séparation de biens entre Jean Dupont et Marie Tremblay',
    erreurs: '- **URGENT**: Discordance dans la description du lot entre l\'acte de 2020 et celui de 2022',
    autres_considerations: '- Hypothèque en faveur de la Banque Nationale (2019)'
  };

  const sampleIndexEntries = [
    {
      id: 'index-1',
      lot_number: '1 234 567',
      cadastre: 'Cadastre du Québec',
      doc_number: 2024001,
      status: 'Completed',
      phase_1_status: 'Analysis Completed',
      phase_2_status: 'Analysis Completed',
      created_at: '2025-01-16',
      related_actes: 3,
      actes_completed: 2,
      is_completed: true
    },
    {
      id: 'index-2',
      lot_number: '1 234 568',
      cadastre: 'Cadastre du Québec',
      doc_number: 2024002,
      status: 'Phase 2',
      phase_1_status: 'Analysis Completed',
      phase_2_status: 'Actes Analysis in Progress',
      created_at: '2025-01-17',
      related_actes: 5,
      actes_completed: 3,
      is_completed: false
    }
  ];

  const sampleActesByIndex = {
    'index-1': [
      {
        id: 'acte-1-1',
        acte_publication_number: '2024-001234',
        acte_nature: 'Vente',
        status: 'Analysis Completed',
        document_completed: true,
        is_radiated: false
      },
      {
        id: 'acte-1-2',
        acte_publication_number: '2019-005678',
        acte_nature: 'Hypothèque',
        status: 'Analysis Completed',
        document_completed: true,
        is_radiated: true,
        radiation_number: '2023-001'
      },
      {
        id: 'acte-1-3',
        acte_publication_number: '2022-009012',
        acte_nature: 'Servitude',
        status: 'Document not Available',
        document_completed: false,
        is_radiated: false
      }
    ],
    'index-2': [
      {
        id: 'acte-2-1',
        acte_publication_number: '2023-003456',
        acte_nature: 'Vente',
        status: 'Analysis Completed',
        document_completed: true
      },
      {
        id: 'acte-2-2',
        acte_publication_number: '2020-007890',
        acte_nature: 'Donation',
        status: 'Analysis Completed',
        document_completed: true
      },
      {
        id: 'acte-2-3',
        acte_publication_number: '2021-002345',
        acte_nature: 'Hypothèque',
        status: 'Analysis Completed',
        document_completed: true
      },
      {
        id: 'acte-2-4',
        acte_publication_number: '2024-000123',
        acte_nature: 'Servitude',
        status: 'Pending',
        document_completed: false
      },
      {
        id: 'acte-2-5',
        acte_publication_number: '2024-000456',
        acte_nature: 'Cession',
        status: 'Pending',
        document_completed: false
      }
    ]
  };

  const metrics = {
    totalIndex: 2,
    completedIndex: 1,
    totalActes: 8,
    completedActes: 5,
    currentPhase: '2',
    daysSinceCreation: 15
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold mb-8">Request Details - New Design Demo</h1>
      
      {/* Metrics Dashboard */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">1. Metrics Dashboard</h2>
        <MetricsDashboard metrics={metrics} />
      </section>

      {/* Document Relationship Graph */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">2. Document Relationship Graph (Mermaid)</h2>
        <DocumentRelationshipGraph 
          indexEntries={sampleIndexEntries} 
          actesByIndex={sampleActesByIndex} 
        />
      </section>

      {/* Filter Bar */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">3. Advanced Filter Bar</h2>
        <Card>
          <CardContent className="pt-6">
            <FilterBar
              onSearch={(query) => console.log('Search:', query)}
              onFilterChange={(filters) => console.log('Filters:', filters)}
              onSortChange={(sort) => console.log('Sort:', sort)}
              onViewChange={(view) => console.log('View:', view)}
              currentView="grid"
              totalItems={8}
              filteredItems={6}
            />
          </CardContent>
        </Card>
      </section>

      {/* Enhanced Index Cards */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">4. Enhanced Document Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {sampleIndexEntries.map((index, idx) => (
            <EnhancedIndexCard
              key={index.id}
              index={index}
              indexNumber={idx + 1}
              actesPreviews={(sampleActesByIndex[index.id] || []).map(acte => ({
                id: acte.id,
                publicationNumber: acte.acte_publication_number,
                nature: acte.acte_nature,
                isCompleted: acte.document_completed
              }))}
              onViewDocument={() => console.log('View document:', index.id)}
              onViewAllActes={() => console.log('View all actes for:', index.id)}
              onDownloadPDF={() => console.log('Download PDF for:', index.id)}
            />
          ))}
        </div>
      </section>

      {/* Analysis Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">5. Analysis Section</h2>
        <AnalysisSection {...sampleRequest} />
      </section>

      {/* Tabbed Interface */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">6. Tabbed Navigation</h2>
        <Card>
          <CardContent className="pt-6">
            <RequestDetailsTabs requestId="demo">
              {{
                overview: <div className="p-4">Overview content here</div>,
                documents: <div className="p-4">Documents tab content</div>,
                analysis: <div className="p-4">Analysis tab content</div>,
                timeline: <div className="p-4">Timeline tab content</div>,
                chat: <div className="p-4">Chat interface here</div>
              }}
            </RequestDetailsTabs>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};

export default RequestDetailsDemo;
```

### 3. Add Route to Your App

Add this route to your router configuration:

```tsx
import RequestDetailsDemo from '@/pages/RequestDetailsDemo';

// In your router setup
<Route path="/request-demo" element={<RequestDetailsDemo />} />
```

### 4. View the Demo

Navigate to `http://localhost:3000/request-demo` (or your dev server URL) to see:

1. **Metrics Dashboard** - Visual key metrics with progress indicators
2. **Document Relationship Graph** - Interactive Mermaid diagram showing document hierarchy
3. **Filter Bar** - Advanced search and filtering interface
4. **Enhanced Document Cards** - Redesigned cards with visual completion indicators
5. **Analysis Section** - Organized display with priority indicators
6. **Tabbed Navigation** - Clean tab interface for content organization

## Key Features Demonstrated

### Visual Improvements
- Color-coded status badges
- Progress indicators and completion percentages
- Visual hierarchy with cards and sections
- Icons for better recognition

### Interactive Elements
- Collapsible sections to reduce information overload
- Zoom controls on the Mermaid graph
- Grid/list view toggle
- Advanced filtering options

### Information Organization
- Tabbed interface for logical content separation
- Progressive disclosure with expandable sections
- Priority-based categorization in analysis
- Clear visual relationships in the graph

## Documentation

For full implementation details, see:
- `/docs/request-details-improvement-plan.md` - Complete improvement plan
- `/docs/request-details-implementation-guide.md` - Step-by-step implementation guide
