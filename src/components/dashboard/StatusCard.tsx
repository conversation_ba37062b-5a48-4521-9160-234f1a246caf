
import React from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';

interface StatusCardProps {
  title: string;
  value: number;
  gradientClass: string;
}

const StatusCard: React.FC<StatusCardProps> = ({ title, value, gradientClass }) => {
  return (
    <Card className={`stat-card bg-gradient-to-br ${gradientClass} text-white shadow-lg border-none`}>
      <CardHeader className="pb-2 pt-4 px-4">
        <CardTitle className="stat-title text-white/90 font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-0">
        <div className="stat-value text-white">{value}</div>
      </CardContent>
    </Card>
  );
};

export default StatusCard;
