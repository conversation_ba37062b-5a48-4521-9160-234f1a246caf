
import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowDown, FileText, AlertTriangle, CalendarDays } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge'; // Import Badge component
import ActeDetailModal from './ActeDetailModal';

interface ActesListProps {
  actes: any[];
}

const ActesList: React.FC<ActesListProps> = ({ actes }) => {
  const [selectedActe, setSelectedActe] = useState<any>(null);
  const [isActeModalOpen, setIsActeModalOpen] = useState(false);
  
  const handleActeClick = (acte: any) => {
    setSelectedActe(acte);
    setIsActeModalOpen(true);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Helper function to determine status badge variant
  const getStatusBadgeVariant = (status: string) => {
    const lowerStatus = status?.toLowerCase() || '';
    
    if (lowerStatus.includes('completed') || lowerStatus.includes('analysis completed')) {
      return "success";
    } else if (lowerStatus.includes('progress') || lowerStatus.includes('analysis in progress')) {
      return "default";
    } else if (lowerStatus.includes('error') || lowerStatus.includes('not available')) {
      return "destructive";
    } else if (lowerStatus.includes('pending') || lowerStatus.includes('ready for analysis')) {
      return "warning";
    } else {
      return "secondary";
    }
  };

  // Function to translate status to French
  const translateStatusToFrench = (status: string): string => {
    if (!status) return '';
    
    const statusMap: {[key: string]: string} = {
      'Pending': 'En attente',
      'Ready for Analysis': 'Prêt à l\'analyse',
      'Analysis in Progress': 'Analyse en cours',
      'Analysis Completed': 'Analyse complété',
      'Document not Available': 'Document non trouvé'
    };
    
    return statusMap[status] || status;
  };

  if (!actes || actes.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground text-sm">
        Aucune inscription trouvée pour cet index.
      </div>
    );
  }

  return (
    <>
      <ScrollArea className="h-[400px] w-full">
        <div className="space-y-3 py-2">
          {actes.map((acte, acteIdx) => (
            <div 
              key={acte.id} 
              className="border rounded-md p-4 relative overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleActeClick(acte);
              }}
            >
              <div className="absolute left-0 top-0 h-full w-1 bg-primary/50"></div>
              
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-base">
                      Inscription {acteIdx + 1}{acte.acte_publication_number ? ` - ${acte.acte_publication_number}` : ''}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {acte.acte_nature || 'Autre'}
                    </Badge>
                    {acte.doc_url && (
                      <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                        <FileText className="h-3 w-3" />
                        Document
                      </Badge>
                    )}
                    {acte.radiation_number && (
                      <Badge variant="destructive" className="flex items-center gap-1 text-xs">
                        <AlertTriangle className="h-3 w-3" />
                        Radié #{acte.radiation_number}
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-2">{acte.acte_details || 'Aucune description'}</p>
                  
                  {acte.acte_publication_date && (
                    <div className="flex items-center text-xs text-muted-foreground">
                      <CalendarDays className="h-3 w-3 mr-1" />
                      Date: {formatDate(acte.acte_publication_date)}
                    </div>
                  )}
                </div>
                
                <div className="flex flex-col items-end gap-2">
                  {acte.status && (
                    <Badge variant={getStatusBadgeVariant(acte.status)} className="text-xs whitespace-nowrap">
                      {translateStatusToFrench(acte.status)}
                    </Badge>
                  )}
                </div>
                
                {acteIdx !== actes.length - 1 && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2">
                    <ArrowDown className="h-4 w-4 text-muted-foreground" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
      
      <ActeDetailModal 
        acte={selectedActe} 
        open={isActeModalOpen} 
        onOpenChange={setIsActeModalOpen} 
      />
    </>
  );
};

export default ActesList;
