
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import ReactMarkdown from 'react-markdown';

interface RequestResearchSummaryProps {
  requestId: string;
}

const RequestResearchSummary: React.FC<RequestResearchSummaryProps> = ({ requestId }) => {
  const [researchSummary, setResearchSummary] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchResearchSummary = async () => {
      try {
        const { data, error } = await supabase
          .from('requests')
          .select('resume_etapes_recherche')
          .eq('id', requestId)
          .single();
        
        if (error) throw error;
        
        setResearchSummary(data?.resume_etapes_recherche || '');
      } catch (error) {
        console.error('Error fetching research summary:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchResearchSummary();
  }, [requestId]);

  return (
    <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
      <div className="border-b border-border/40 px-6 py-4">
        <h2 className="text-xl font-semibold">Résumé des étapes de la recherche</h2>
      </div>
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center p-4">
            <p className="text-muted-foreground">Chargement...</p>
          </div>
        ) : researchSummary ? (
          <div className="markdown-content prose prose-sm dark:prose-invert max-w-none">
            <ReactMarkdown>
              {researchSummary}
            </ReactMarkdown>
          </div>
        ) : (
          <div className="flex items-center justify-center p-4">
            <p className="text-muted-foreground italic">Aucune étape de recherche n'a été enregistrée</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RequestResearchSummary;
