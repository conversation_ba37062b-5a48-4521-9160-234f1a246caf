
import React from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { ChevronLeft, Search, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const NotFoundState: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1 container px-4 sm:px-6 lg:px-8 py-12 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 flex flex-col items-center text-center space-y-6">
            <div className="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
              <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Demande non trouvée</h2>
              <p className="text-muted-foreground">
                La demande avec l'identifiant <span className="font-mono bg-muted px-1 py-0.5 rounded text-sm">{id}</span> n'existe pas ou a été supprimée.
              </p>
            </div>
            
            <div className="space-y-3 w-full pt-2">
              <Button asChild className="w-full">
                <Link to="/">
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Retour à la liste des demandes
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full" onClick={() => navigate(-1)}>
                Retour à la page précédente
              </Button>
              
              <Button variant="ghost" asChild className="w-full">
                <Link to="/">
                  <Search className="mr-2 h-4 w-4" />
                  Rechercher une autre demande
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default NotFoundState;
