
import React from 'react';
import { Card } from '@/components/ui/card';
import IndexEntry from './IndexEntry'; // Ensure this is correctly imported

interface IndexEntriesListProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
}

const IndexEntriesList: React.FC<IndexEntriesListProps> = ({ 
  indexEntries, 
  actesByIndex
}) => {
  if (indexEntries.length === 0) {
    return (
      <Card className="bg-muted">
        <div className="py-6 text-center">
          <p className="text-muted-foreground">Aucun index trouvé pour cette demande.</p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4 pb-8">
      {indexEntries.map((index: any, idx: number) => (
        <IndexEntry 
          key={index.id} 
          index={index} 
          indexNumber={idx + 1}
          actes={actesByIndex[index.id] || []}
        />
      ))}
    </div>
  );
};

export default IndexEntriesList;
