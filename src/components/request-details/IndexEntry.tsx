
import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { FileText, Clock, MapPin, Hash } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge'; // Import Badge component
import ActesList from './ActesList';
import IndexDetailModal from './IndexDetailModal';

interface IndexEntryProps {
  index: any;
  indexNumber: number;
  actes: any[];
}

const IndexEntry: React.FC<IndexEntryProps> = ({ index, indexNumber, actes }) => {
  const [isIndexModalOpen, setIsIndexModalOpen] = useState(false);
  
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Helper function to determine status badge variant
  const getStatusBadgeVariant = (status: string) => {
    const lowerStatus = status?.toLowerCase() || '';
    
    if (lowerStatus.includes('completed')) {
      return "success";
    } else if (lowerStatus.includes('progress') || lowerStatus.includes('phase')) {
      return "default";
    } else if (lowerStatus.includes('error') || lowerStatus.includes('not available')) {
      return "destructive";
    } else if (lowerStatus.includes('pending')) {
      return "warning";
    } else {
      return "secondary";
    }
  };

  return (
    <>
      <Card className="border-l-[5px] overflow-hidden hover:shadow-md transition-shadow cursor-pointer relative" 
        style={{ borderLeftColor: '#3E5AB3' }}
        onClick={() => setIsIndexModalOpen(true)}
      >
        <CardHeader className="pb-3 bg-muted/20">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
            <CardTitle className="text-lg font-semibold flex flex-wrap items-center gap-2">
              <span className="mr-1">Index {indexNumber} - {index.lot_number}</span>
              {index.status && (
                <Badge variant={getStatusBadgeVariant(index.status)} className="text-xs">
                  {index.status}
                </Badge>
              )}
            </CardTitle>
            
            <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
              {index.cadastre && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {index.cadastre}
                </Badge>
              )}
              {index.doc_number && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Hash className="h-3 w-3" />
                  Document #{index.doc_number}
                </Badge>
              )}
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Créé le {formatDate(index.created_at || '')}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-4">
          <Accordion type="single" collapsible className="w-full" onClick={(e) => e.stopPropagation()}>
            <AccordionItem value="actes" className="border-b-0">
              <AccordionTrigger className="py-3 hover:no-underline">
                <div className="flex items-center px-2 py-1 bg-accent/30 rounded-md">
                  <FileText className="h-4 w-4 mr-2" />
                  <span className="font-medium">
                    Inscriptions ({actes?.length || 0})
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="mt-3">
                  <ActesList actes={actes} />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
      
      <IndexDetailModal 
        index={index} 
        open={isIndexModalOpen} 
        onOpenChange={setIsIndexModalOpen} 
      />
    </>
  );
};

export default IndexEntry;
