import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const RouteDebugger: React.FC = () => {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(false);
  const [routeHistory, setRouteHistory] = useState<string[]>([]);

  useEffect(() => {
    // Add current path to history
    setRouteHistory(prev => [...prev, location.pathname].slice(-10));
    
    // Log route change for debugging
    console.log('Route changed:', {
      pathname: location.pathname,
      search: location.search,
      hash: location.hash,
      state: location.state
    });
  }, [location]);

  if (!import.meta.env.DEV) {
    return null;
  }

  if (!isVisible) {
    return (
      <Button
        size="sm"
        variant="outline"
        className="fixed bottom-4 right-4 z-50 opacity-50 hover:opacity-100"
        onClick={() => setIsVisible(true)}
      >
        Debug Routes
      </Button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-w-[90vw] bg-background border rounded-lg shadow-lg p-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold">Route Debugger</h3>
        <Button size="sm" variant="ghost" onClick={() => setIsVisible(false)}>
          <X className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="space-y-2">
        <div>
          <span className="text-sm font-medium">Current Path:</span>
          <code className="ml-2 p-1 bg-muted rounded text-xs">{location.pathname}</code>
        </div>
        
        <div>
          <span className="text-sm font-medium">Query Params:</span>
          <code className="ml-2 p-1 bg-muted rounded text-xs">{location.search || '(none)'}</code>
        </div>
        
        <div>
          <span className="text-sm font-medium">Route History:</span>
          <div className="mt-1 max-h-32 overflow-y-auto">
            {routeHistory.map((path, i) => (
              <div key={i} className="text-xs py-1 border-b border-border/30 last:border-0">
                {path}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RouteDebugger;