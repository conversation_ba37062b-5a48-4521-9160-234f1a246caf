
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme variables - new earthy palette */
    --background: 48 33% 94%;
    --foreground: 145 42% 16%;

    --card: 48 30% 96%;
    --card-foreground: 145 42% 16%;

    --popover: 48 30% 96%;
    --popover-foreground: 145 42% 16%;

    --primary: 145 42% 16%;
    --primary-foreground: 48 33% 94%;

    --secondary: 48 15% 90%;
    --secondary-foreground: 145 42% 16%;

    --muted: 48 15% 90%;
    --muted-foreground: 145 30% 30%;

    --accent: 145 20% 88%;
    --accent-foreground: 145 42% 16%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 48 33% 94%;

    --border: 48 15% 83%;
    --input: 48 15% 83%;
    --ring: 145 42% 16%;

    --radius: 0.5rem;
  }

  .dark {
    /* Improved dark theme with enhanced contrast */
    --background: 145 22% 7%;
    --foreground: 48 33% 95%;

    --card: 145 24% 11%;
    --card-foreground: 48 33% 96%;

    --popover: 145 24% 11%;
    --popover-foreground: 48 33% 96%;

    --primary: 145 36% 34%;
    --primary-foreground: 48 33% 96%;

    --secondary: 145 18% 19%;
    --secondary-foreground: 48 33% 96%;

    --muted: 145 18% 19%;
    --muted-foreground: 145 15% 75%;

    --accent: 145 18% 26%;
    --accent-foreground: 145 36% 45%;

    --destructive: 0 65% 42%;
    --destructive-foreground: 48 33% 95%;

    --border: 145 18% 25%;
    --input: 145 18% 25%;
    --ring: 145 36% 34%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground min-h-screen transition-colors duration-200;
    font-family: -apple-system, BlinkMacSystemFont, 'San Francisco', 'SF Pro Text', 'SF Pro Display', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: -apple-system, BlinkMacSystemFont, 'San Francisco', 'SF Pro Text', 'SF Pro Display', system-ui, sans-serif;
  }

  /* Light theme background with grain texture */
  body {
    @apply bg-gradient-to-br from-[#f3eee2] via-[#f0ebe0] to-[#f3eee2];
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%23a08f62' fill-opacity='0.07' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E"), 
                      linear-gradient(to bottom right, #f3eee2, #f0ebe0, #f3eee2);
  }

  /* Dark theme background with improved grain texture */
  .dark body {
    @apply bg-gradient-to-br from-[#0d2620] via-[#091f19] to-[#07191b];
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%23a08f62' fill-opacity='0.04' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E"),
                      linear-gradient(to bottom right, #0d2620, #091f19, #07191b);
  }

  /* Custom scrollbar - improved for dark mode */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background/50;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/40;
  }
  
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-primary/40 rounded-full;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/60;
  }
}

@layer components {
  .card-shadow {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    @apply transition-all duration-300;
  }

  .card-shadow:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
  
  .dark .card-shadow {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }
  
  .dark .card-shadow:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
  }

  .glass-panel {
    @apply bg-white/5 backdrop-blur-xl border border-white/10;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary via-[#1f5e42] to-primary bg-clip-text text-transparent;
  }
  
  .dark .gradient-text {
    @apply bg-gradient-to-r from-[#3b8a6a] via-[#2d7b5d] to-[#3b8a6a] bg-clip-text text-transparent;
  }
  
  .stat-card {
    @apply rounded-xl p-5 flex flex-col h-full;
  }
  
  .stat-title {
    @apply text-sm font-medium text-muted-foreground mb-1;
  }
  
  .stat-value {
    @apply text-4xl font-bold;
  }
  
  .dashboard-card {
    @apply bg-card rounded-xl border border-border/40 p-4 hover:border-primary/20 transition-all;
  }
  
  .sidebar-link {
    @apply flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors py-2;
  }
  
  .sidebar-link-active {
    @apply text-primary font-medium;
  }
  
  /* Markdown content styling */
  .markdown-content {
    @apply w-full text-foreground;
  }
  
  .markdown-content p {
    @apply mb-2;
  }
  
  .markdown-content ul {
    @apply list-disc pl-5 mb-2;
  }
  
  .markdown-content ol {
    @apply list-decimal pl-5 mb-2;
  }
  
  .markdown-content h1,
  .markdown-content h2,
  .markdown-content h3,
  .markdown-content h4,
  .markdown-content h5,
  .markdown-content h6 {
    @apply font-semibold mb-2 mt-3;
  }
  
  .markdown-content h1 {
    @apply text-xl;
  }
  
  .markdown-content h2 {
    @apply text-lg;
  }
  
  .markdown-content a {
    @apply text-primary underline hover:text-primary/80;
  }
  
  .markdown-content code {
    @apply bg-muted px-1.5 py-0.5 rounded text-sm;
  }
  
  .markdown-content pre {
    @apply bg-muted p-2 rounded my-2 overflow-x-auto;
  }
  
  .markdown-content blockquote {
    @apply border-l-4 border-primary/30 pl-3 py-1 italic;
  }
}

