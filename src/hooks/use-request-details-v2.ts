import { useState, useEffect } from 'react';
import { transformRequestData, calculateMetrics } from '@/lib/transformers/request-details-transformer';
// Assuming supabase client is available globally or imported
import { supabase } from '@/lib/supabase'; // Adjust path as necessary

export const useRequestDetailsV2 = (requestId: string) => {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchRequestDetails = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch request data
        const { data: requestData, error: requestError } = await supabase
          .from('requests')
          .select('*')
          .eq('id', requestId)
          .single();

        if (requestError) throw requestError;

        // Fetch index entries
        const { data: indexEntriesData, error: indexError } = await supabase
          .from('index')
          .select('*')
          .eq('request_id', requestId);

        if (indexError) throw indexError;

        // Fetch actes for each index entry
        const actesByIndex: { [key: string]: any[] } = {};
        for (const indexEntry of indexEntriesData) {
          const { data: actesData, error: actesError } = await supabase
            .from('actes')
            .select('*')
            .eq('index_id', indexEntry.id);
          
          if (actesError) throw actesError;
          actesByIndex[indexEntry.id] = actesData;
        }

        // Transform and calculate metrics
        const transformedRequest = transformRequestData(requestData);
        const metrics = calculateMetrics(requestData, indexEntriesData, actesByIndex);

        setData({
          request: transformedRequest,
          indexEntries: indexEntriesData,
          actesByIndex,
          metrics,
        });

      } catch (err: any) {
        console.error("Error fetching request details:", err.message);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (requestId) {
      fetchRequestDetails();
    }
  }, [requestId]);
  
  return { data, isLoading, error };
};
