
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import TopNavbar from "@/components/TopNavbar";
import Index from "./pages/Index";
import NewRequest from "./pages/NewRequest";
import WebhookConfiguration from "./pages/WebhookConfiguration";
import NotFound from "./pages/NotFound";
import RequestDetails from "./pages/RequestDetails";
import Auth from "./pages/Auth";
import ResetPassword from "./pages/ResetPassword";
import Profile from "./pages/Profile";
import RequestDetailsDemo from "./pages/RequestDetailsDemo";
import RequestDetailsV2 from "./pages/RequestDetailsV2"; // Import the new V2 component
import { ThemeProvider } from "@/components/ThemeProvider";
import PageFooter from "@/components/PageFooter";
import { AuthProvider } from "@/context/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import RouteDebugger from "@/components/RouteDebugger";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

// Animation wrapper component
const AnimatedRoutes = () => {
  const location = useLocation();
  
  // Don't show navbar on auth and reset-password pages
  const isPublicPage = location.pathname === '/auth' || location.pathname === '/reset-password';
  
  if (isPublicPage) {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={location.pathname}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Routes location={location}>
            <Route path="/auth" element={<Auth />} />
            <Route path="/reset-password" element={<ResetPassword />} />
          </Routes>
        </motion.div>
      </AnimatePresence>
    );
  }
  
  return (
    <>
      <TopNavbar />
      <div className="min-h-screen">
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="flex-1 flex flex-col"
          >
            <Routes location={location}>
              <Route path="/" element={
                <ProtectedRoute>
                  <Index />
                </ProtectedRoute>
              } />
              <Route path="/new-request" element={
                <ProtectedRoute>
                  <NewRequest />
                </ProtectedRoute>
              } />
              <Route path="/requests/:id" element={
                <ProtectedRoute>
                  <RequestDetails />
                </ProtectedRoute>
              } />
              <Route path="/webhook-configuration" element={
                <ProtectedRoute>
                  <WebhookConfiguration />
                </ProtectedRoute>
              } />
              <Route path="/profile" element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              } />
              <Route path="/request-demo" element={<RequestDetailsDemo />} />
              <Route path="/requests-v2/:id" element={
                <ProtectedRoute>
                  <RequestDetailsV2 />
                </ProtectedRoute>
              } />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </motion.div>
        </AnimatePresence>
      </div>
      <PageFooter />
    </>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <ThemeProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner position="top-right" closeButton expand={false} />
          <BrowserRouter>
            <div className="flex flex-col min-h-screen">
              <AnimatedRoutes />
              <RouteDebugger />
            </div>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
