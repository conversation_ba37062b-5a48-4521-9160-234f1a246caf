import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';
import type { NotaryRequest } from './data';

// Create a single instance of the Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

// Ensure we only create one client instance
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: 'notaire-qc-auth'
  }
});

// Export a function to get the client instead of creating multiple instances
export const getSupabaseClient = () => supabase;

// Legacy interface maintained for compatibility with existing code
export interface SupabaseNotaryRequest {
  id: string;
  client_id: string;
  numero_lot: string | null;
  circonscription_fonciere: string | null;
  paroisse_cadastrale: string | null;
  index_initial: boolean;
  document_state: string | null;
  document_id: string | null;
  document_link: string | null;
  document_result: string | null;
  document_summary: string | null;
  nombre_concordances: number;
  nombre_actes: number;
  document_number: string | null;
  status: string;
  seller_name: string | null;
  created_at: string;
  updated_at: string;
}

// Updated interface to match the 'index' table
export interface IndexEntryNew {
  id?: string;
  request_id: string;
  lot_number?: string;
  circonscription?: string;
  cadastre?: string;
  index_initial: boolean;
  number_of_concordances?: number;
  number_of_actes?: number;
  doc_id?: string;
  doc_url?: string;
  doc_number?: number;
  relevance_rating?: number;
  index_summary?: string;
  relevance_explanation?: string;
  created_at?: string;
  updated_at?: string;
  // Fields added based on error messages
  status?: Database["public"]["Enums"]["index_status_enum"];
  phase_1_status?: Database["public"]["Enums"]["index_phase_status_enum"];
  document_acquisition_round?: number;
  document_ready?: boolean;
  actes_completed?: number;
  related_actes?: number;
  phase_1_completed?: boolean;
  phase_2_completed?: boolean;
  phase_3_completed?: boolean;
  is_completed?: boolean;
  designation_secondaire?: string;
}

// Legacy interface, kept for backward compatibility
export interface IndexEntry {
  id?: string;
  notary_request_id: string;
  index_name: string;
  index_type?: string;
  index_date?: string;
  index_status?: string;
  index_data?: any;
  created_at?: string;
  updated_at?: string;
}

// Legacy interface, kept for backward compatibility
export interface ActeEntry {
  id?: string;
  index_entry_id: string;
  acte_number: string;
  acte_date?: string;
  acte_type?: string;
  acte_description?: string;
  acte_data?: any;
  created_at?: string;
  updated_at?: string;
}

// Interface for new 'requests' table
export interface RequestEntry {
  id?: string;
  seller_name: string;
  seller_address: string;
  user_id: string;
  request_summary: string; // Made non-optional to match database requirements
  number_of_index?: number;
  number_of_actes?: number;
  current_step?: string;
  status?: string; // Changed to string type since enum isn't available
  complete_summary?: string[];
  created_at?: string;
  updated_at?: string;
}

export const mapToNotaryRequest = (request: any): NotaryRequest => {
  // Map database status values to the expected UI status values
  let status: 'pending' | 'approved' | 'rejected' | 'inprogress';
  
  // Handle different status field names and values from both old and new schemas
  const rawStatus = request.status || 'pending';
  
  // Map the database status values to our UI status values
  switch (rawStatus.toLowerCase()) {
    case 'analysis completed':
    case 'completed':
    case 'approved':
      status = 'approved';
      break;
    case 'in progress':
    case 'inprogress':
    case 'processing':
      status = 'inprogress';
      break;
    case 'rejected':
    case 'failed':
      status = 'rejected';
      break;
    case 'pending':
    default:
      status = 'pending';
      break;
  }
  
  return {
    id: request.id,
    created_at: request.created_at,
    seller_name: request.seller_name || 'Unknown Seller',
    status: status,
    property_address: request.circonscription_fonciere || request.seller_address || undefined,
    request_type: request.document_state || request.current_step || undefined,
  };
};

export const fetchNotaryRequests = async (): Promise<NotaryRequest[]> => {
  try {
    const { data, error } = await supabase
      .from('requests')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching notary requests:', error);
      return [];
    }
    
    return (data as any[]).map(mapToNotaryRequest);
  } catch (error) {
    console.error('Failed to fetch notary requests:', error);
    return [];
  }
};

export const createRequest = async (requestData: RequestEntry): Promise<{ data: RequestEntry | null, error: any }> => {
  try {
    // We no longer need to validate enum values since status is now a string
    
    // Make sure request_summary is provided if not already
    if (!requestData.request_summary) {
      requestData.request_summary = `Title search request for ${requestData.seller_name}`;
    }
    
    console.log('Creating request with data:', requestData);
    
    const { data, error } = await supabase
      .from('requests')
      .insert(requestData)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating request:', error);
      return { data: null, error };
    }
    
    return { data: data as RequestEntry, error: null };
  } catch (error) {
    console.error('Failed to create request:', error);
    return { data: null, error };
  }
};

export const createIndex = async (indexData: IndexEntryNew): Promise<{ data: IndexEntryNew | null, error: any }> => {
  try {
    // Validate the status value is a valid enum
    const validStatusValues: Database["public"]["Enums"]["index_status_enum"][] = ['Phase 1', 'Phase 2', 'Phase 3', 'Completed'];
    
    if (!indexData.status || !validStatusValues.includes(indexData.status)) {
      console.log('Invalid status value detected. Setting to default: Phase 1');
      indexData.status = 'Phase 1' as Database["public"]["Enums"]["index_status_enum"];
    }
    
    console.log('Creating index with data:', indexData);
    
    const { data, error } = await supabase
      .from('index')
      .insert(indexData)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating index entry:', error);
      return { data: null, error };
    }
    
    return { data: data as IndexEntryNew, error: null };
  } catch (error) {
    console.error('Failed to create index entry:', error);
    return { data: null, error };
  }
};

// Legacy functions kept for backward compatibility, but they would need updating
// to work with the new schema if they are actually used
export const createIndexEntry = async (indexEntry: any): Promise<{ data: any | null, error: any }> => {
  console.warn('createIndexEntry is deprecated - use createIndex instead');
  try {
    console.log('Would create index entry:', indexEntry);
    return { data: null, error: new Error('Function deprecated - use createIndex instead') };
  } catch (error) {
    console.error('Failed to create index entry:', error);
    return { data: null, error };
  }
};

export const createActeEntry = async (acteEntry: any): Promise<{ data: any | null, error: any }> => {
  console.warn('createActeEntry is deprecated');
  try {
    console.log('Would create acte entry:', acteEntry);
    return { data: null, error: new Error('Function deprecated - schema has changed') };
  } catch (error) {
    console.error('Failed to create acte entry:', error);
    return { data: null, error };
  }
};

export const fetchActesForIndexEntry = async (indexEntryId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('actes')
      .select('*')
      .eq('index_id', indexEntryId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching actes:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Failed to fetch actes:', error);
    return [];
  }
};

export const updateIndexStatus = async (
  indexId: string, 
  status: Database["public"]["Enums"]["index_status_enum"]
): Promise<{ success: boolean; error: any }> => {
  try {
    const { error } = await supabase
      .from('index')
      .update({ status: status })
      .eq('id', indexId);
    
    if (error) {
      console.error('Error updating index status:', error);
      return { success: false, error };
    }
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Failed to update index status:', error);
    return { success: false, error };
  }
};
