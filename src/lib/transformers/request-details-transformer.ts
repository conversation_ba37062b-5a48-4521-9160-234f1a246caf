export const transformRequestData = (request: any) => ({
  // Map database fields to component props
  servitudes: request.servitudes,
  regimesMatrimoniaux: request.regimes_matrimoniaux,
  erreurs: request.erreurs,
  autresConsiderations: request.autres_considerations
});

export const calculateMetrics = (request: any, indexEntries: any[], actesByIndex: any) => ({
  totalIndex: indexEntries.length,
  completedIndex: indexEntries.filter(i => i.is_completed).length,
  totalActes: Object.values(actesByIndex).flat().length,
  completedActes: Object.values(actesByIndex).flat().filter((a: any) => a.document_completed).length,
  currentPhase: extractPhaseFromStatus(request.status),
  daysSinceCreation: calculateDaysSince(request.created_at)
});

const extractPhaseFromStatus = (status: string) => {
  const lowerStatus = status?.toLowerCase();
  if (lowerStatus?.includes('phase 1 completed') || lowerStatus?.includes('phase 2 in progress') || lowerStatus?.includes('phase 2 completed') || lowerStatus?.includes('phase 3 in progress') || lowerStatus?.includes('completed')) {
    return "3";
  } else if (lowerStatus?.includes('phase 1 in progress') || lowerStatus?.includes('ready for analysis') || lowerStatus?.includes('analysis in progress') || lowerStatus?.includes('analysis completed')) {
    return "2";
  } else if (lowerStatus?.includes('pending') || lowerStatus?.includes('created')) {
    return "1";
  }
  return "N/A";
};

const calculateDaysSince = (createdAt: string) => {
  if (!createdAt) return 0;
  const createdDate = new Date(createdAt);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - createdDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};
