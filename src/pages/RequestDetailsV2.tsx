import { useParams, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import NotFoundState from '@/components/request-details/NotFoundState';
import LoadingState from '@/components/ui/loading-state';
import MetricsDashboard from '@/components/request-details-v2/MetricsDashboard';
import LoadingSpinner from '@/components/request-details/LoadingSpinner';
import RequestDetailsTabs from '@/components/request-details-v2/RequestDetailsTabs';
import RequestDetailsHeader from '@/components/request-details/RequestDetailsHeader';
import RequestResearchSummary from '@/components/request-details/RequestResearchSummary';
import AnalysisSection from '@/components/request-details-v2/AnalysisSection';
import DocumentRelationshipGraph from '@/components/request-details-v2/DocumentRelationshipGraph';
import FilterBar from '@/components/request-details-v2/FilterBar';
import EnhancedIndexCard from '@/components/request-details-v2/EnhancedIndexCard';
import ActesList from '@/components/request-details/ActesList'; // Re-using existing component for now
import IndexEntriesList from '@/components/request-details/IndexEntriesList'; // Re-using existing component for now
import { Card } from '@/components/ui/card'; // Import Card component

const RequestDetailsV2 = () => {
  const { id } = useParams<{ id: string }>();
  const [request, setRequest] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchRequest = async () => {
      if (!id) {
        setError("ID de demande manquant");
        setLoading(false);
        return;
      }

      if (!user) {
        // If not logged in, we'll let the ProtectedRoute handle the redirect
        return;
      }

      try {
        console.log(`Fetching request with ID: ${id}`);
        
        const { data, error } = await supabase
          .from('requests')
          .select('*')
          .eq('id', id)
          .single();
        
        if (error) {
          console.error('Error fetching request:', error);
          
          if (error.code === 'PGRST116') {
            setError("Cette demande n'existe pas");
            toast({
              title: "Demande introuvable",
              description: `La demande avec l'ID ${id} n'a pas été trouvée.`,
              variant: "destructive"
            });
          } else {
            setError("Erreur lors du chargement de la demande");
            toast({
              title: "Erreur",
              description: "Impossible de charger les détails de la demande.",
              variant: "destructive"
            });
          }
        } else if (!data) {
          setError("Cette demande n'existe pas");
          toast({
            title: "Demande introuvable",
            description: `La demande avec l'ID ${id} n'a pas été trouvée.`,
            variant: "destructive"
          });
        } else {
          console.log('Request data loaded successfully:', data);
          setRequest(data);
        }
      } catch (error) {
        console.error('Unexpected error fetching request:', error);
        setError("Une erreur inattendue s'est produite");
      } finally {
        setLoading(false);
      }
    };

    fetchRequest();
  }, [id, user, toast]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Chargement des détails de la demande...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="max-w-md w-full bg-card rounded-lg shadow p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Demande introuvable</h2>
          <p className="text-muted-foreground mb-6">{error}</p>
          <div className="space-y-3">
            <button 
              className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              onClick={() => navigate('/')}
            >
              Retour à la liste des demandes
            </button>
            <button 
              className="w-full px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90"
              onClick={() => navigate(-1)}
            >
              Retour à la page précédente
            </button>
          </div>
        </div>
      </div>
    );
  }

  const { indexEntries, actesByIndex, metrics } = request;

  const allDocuments = useMemo(() => {
    const indexes = indexEntries.map((index: any) => ({
      ...index,
      type: 'index',
      displayDate: index.created_at,
      displayName: `Index ${index.lot_number}`,
    }));
    const actes = Object.values(actesByIndex).flat().map((acte: any) => ({
      ...acte,
      type: 'acte',
      displayDate: acte.created_at, // Assuming actes also have a created_at
      displayName: `${acte.acte_nature} ${acte.acte_publication_number}`,
    }));
    return [...indexes, ...actes];
  }, [indexEntries, actesByIndex]);

  const filteredDocuments = useMemo(() => {
    let filtered = allDocuments;

    // Apply search
    if (searchQuery) {
      filtered = filtered.filter(doc =>
        doc.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (doc.cadastre && doc.cadastre.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (doc.doc_number && doc.doc_number.toString().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply filters
    if (filters.type !== 'all') {
      filtered = filtered.filter(doc => doc.type === filters.type);
    }
    if (filters.status !== 'all') {
      filtered = filtered.filter(doc => {
        const status = doc.status?.toLowerCase();
        if (filters.status === 'completed') return status?.includes('completed');
        if (filters.status === 'pending') return status?.includes('pending') || status?.includes('progress') || status?.includes('phase');
        if (filters.status === 'error') return status?.includes('error') || status?.includes('not available');
        return true;
      });
    }
    // Date range filter logic would go here, requires more complex date comparisons

    return filtered;
  }, [allDocuments, searchQuery, filters]);

  const sortedDocuments = useMemo(() => {
    let sorted = [...filteredDocuments];
    sorted.sort((a, b) => {
      if (sortOption === 'date-desc') {
        return new Date(b.displayDate).getTime() - new Date(a.displayDate).getTime();
      }
      if (sortOption === 'date-asc') {
        return new Date(a.displayDate).getTime() - new Date(b.displayDate).getTime();
      }
      if (sortOption === 'name-asc') {
        return a.displayName.localeCompare(b.displayName);
      }
      if (sortOption === 'name-desc') {
        return b.displayName.localeCompare(a.displayName);
      }
      if (sortOption === 'status') {
        return a.status.localeCompare(b.status);
      }
      return 0;
    });
    return sorted;
  }, [filteredDocuments, sortOption]);

  const handleFilterChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
  }, []);

  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleSortChange = useCallback((sort: string) => {
    setSortOption(sort);
  }, []);

  const handleViewChange = useCallback((view: 'grid' | 'list') => {
    setCurrentView(view);
  }, []);

  return (
    <div className="container mx-auto p-4">
      <RequestDetailsHeader request={request} />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        <div className="md:col-span-2">
          <MetricsDashboard metrics={metrics} />
        </div>
        <div className="md:col-span-1">
          <RequestResearchSummary requestId={id || ''} />
        </div>
      </div>

      <RequestDetailsTabs requestId={id || ''}>
        {{
          overview: (
            <>
              <h2 className="text-xl font-semibold mb-4">Property Information</h2>
              <p><strong>Servitudes:</strong> {request.servitudes}</p>
              <p><strong>Regimes Matrimoniaux:</strong> {request.regimesMatrimoniaux}</p>
              <p><strong>Erreurs:</strong> {request.erreurs}</p>
              <p><strong>Autres Considerations:</strong> {request.autresConsiderations}</p>
            </>
          ),
          documents: (
            <>
              <FilterBar 
                onFilterChange={handleFilterChange} 
                onSearch={handleSearchChange} 
                onSortChange={handleSortChange} 
                onViewChange={handleViewChange} 
                currentView={currentView}
                totalItems={allDocuments.length}
                filteredItems={filteredDocuments.length}
              />
              <div className={currentView === 'grid' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4" : "space-y-4 mt-4"}>
                {sortedDocuments.map((doc: any) => {
                  if (doc.type === 'index') {
                    if (currentView === 'grid') {
                      return (
                        <EnhancedIndexCard 
                          key={doc.id} 
                          index={doc} 
                          indexNumber={indexEntries.indexOf(doc) + 1}
                          actesPreviews={(actesByIndex[doc.id] || []).map((acte: any) => ({
                            id: acte.id,
                            publicationNumber: acte.acte_publication_number,
                            nature: acte.acte_nature,
                            isCompleted: acte.document_completed,
                          }))}
                          onViewDocument={() => console.log('View document for', doc.id)}
                          onViewAllActes={() => console.log('View all actes for', doc.id)}
                          onDownloadPDF={() => console.log('Download PDF for', doc.id)}
                        />
                      );
                    } else {
                      return (
                        <IndexEntriesList 
                          key={doc.id} 
                          indexEntries={[doc]} // Pass as array for IndexEntriesList
                          actesByIndex={actesByIndex}
                        />
                      );
                    }
                  } else {
                    // Render Acte as a simple card or list item for now
                    return (
                      <Card key={doc.id} className="p-4">
                        <h4 className="font-semibold">{doc.displayName}</h4>
                        <p className="text-sm text-muted-foreground">{doc.acte_nature} - {doc.status}</p>
                      </Card>
                    );
                  }
                })}
              </div>
            </>
          ),
          analysis: (
            <>
              <AnalysisSection 
                servitudes={request.servitudes}
                regimesMatrimoniaux={request.regimesMatrimoniaux}
                erreurs={request.erreurs}
                autresConsiderations={request.autresConsiderations}
                indexEntries={indexEntries}
                actesByIndex={actesByIndex}
              />
              <DocumentRelationshipGraph indexEntries={indexEntries} actesByIndex={actesByIndex} />
            </>
          ),
          timeline: (
            <>
              <h2 className="text-xl font-semibold mb-4">Timeline</h2>
              <p>Timeline content will go here.</p>
            </>
          ),
          chat: (
            <>
              <h2 className="text-xl font-semibold mb-4">Chat</h2>
              <p>Chat functionality will go here.</p>
            </>
          ),
        }}
      </RequestDetailsTabs>
    </div>
  );
};

export default RequestDetailsV2;
