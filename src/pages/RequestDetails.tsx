import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { ChevronLeft, FileText } from 'lucide-react';
import RequestDetailsHeader from '@/components/request-details/RequestDetailsHeader';
import IndexEntriesList from '@/components/request-details/IndexEntriesList';
import RequestDetailsSummary from '@/components/request-details/RequestDetailsSummary';
import LoadingSpinner from '@/components/request-details/LoadingSpinner';
import NotFoundState from '@/components/request-details/NotFoundState';
import RequestChatInterface from '@/components/request-details/RequestChatInterface';
import RequestTabbedSection from '@/components/request-details/RequestTabbedSection';
import RequestResearchSummary from '@/components/request-details/RequestResearchSummary';

const RequestDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const [request, setRequest] = useState<any | null>(null);
  const [indexEntries, setIndexEntries] = useState<any[]>([]);
  const [actesByIndex, setActesByIndex] = useState<{ [key: string]: any[] }>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchRequestDetails = async () => {
      setIsLoading(true);
      
      try {
        // Fetch the request
        const { data: requestData, error: requestError } = await supabase
          .from('requests')
          .select('*')
          .eq('id', id)
          .single();
        
        if (requestError) throw requestError;
        
        setRequest(requestData);
        
        // Fetch index entries
        const { data: indexData, error: indexError } = await supabase
          .from('index')
          .select('*')
          .eq('request_id', id)
          .order('created_at', { ascending: false });
        
        if (indexError) throw indexError;
        
        // Sort index entries by doc_number if it exists
        const sortedIndexData = [...indexData].sort((a, b) => {
          // Handle missing values or NaN
          if (!a.doc_number && !b.doc_number) return 0;
          if (!a.doc_number) return 1;
          if (!b.doc_number) return -1;
          
          return Number(a.doc_number) - Number(b.doc_number);
        });
        
        setIndexEntries(sortedIndexData);
        
        // Fetch actes for the request
        const { data: actesData, error: actesError } = await supabase
          .from('actes')
          .select('*')
          .eq('request_id', id)
          .order('created_at', { ascending: false });
        
        if (actesError) throw actesError;
        
        // Group actes by index_id
        const actesMap: { [key: string]: any[] } = {};
        
        if (actesData) {
          sortedIndexData.forEach(index => {
            actesMap[index.id] = actesData.filter(acte => acte.index_id === index.id) || [];
          });
        }
        
        setActesByIndex(actesMap);
      } catch (error) {
        console.error('Error fetching request details:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de charger les détails de la demande. Veuillez réessayer plus tard.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    if (id) {
      fetchRequestDetails();
    }
  }, [id, toast]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!request) {
    return <NotFoundState />;
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <main className="flex-1 container max-w-screen-xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Link to="/" className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Retour au tableau de bord
          </Link>
        </div>

        <RequestDetailsSummary request={request} indexCount={indexEntries.length} acteCount={Object.values(actesByIndex).flat().length} />
        
        <div className="space-y-8 mt-8">
          <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
            <div className="border-b border-border/40 px-6 py-4">
              <h2 className="text-xl font-semibold">Détails de la demande</h2>
            </div>
            <div className="p-6">
              <RequestDetailsHeader request={request} />
            </div>
          </div>
          
          {/* Add the new research summary section */}
          {id && <RequestResearchSummary requestId={id} />}
          
          {/* Existing tabbed section */}
          {id && <RequestTabbedSection requestId={id} />}
          
          <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
            <div className="border-b border-border/40 px-6 py-4">
              <h2 className="text-xl font-semibold">Index et inscriptions</h2>
            </div>
            <div className="p-6">
              <IndexEntriesList 
                indexEntries={indexEntries} 
                actesByIndex={actesByIndex} 
              />
            </div>
          </div>

          {/* Chat Interface */}
          {id && (
            <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
              <div className="p-0">
                <RequestChatInterface requestId={id} />
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default RequestDetails;
