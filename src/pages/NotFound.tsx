import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Home, Search } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  // Check if this is a request details page
  const isRequestPage = location.pathname.includes('/requests') || location.pathname.includes('/requests-v2');
  const requestId = isRequestPage ? location.pathname.split('/').pop() : null;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4">404</h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            {isRequestPage 
              ? `La demande avec l'identifiant "${requestId}" n'a pas été trouvée.` 
              : "La page que vous recherchez n'existe pas."}
          </p>
          
          <div className="space-y-3">
            <Button asChild variant="default" className="w-full">
              <Link to="/" className="flex items-center justify-center gap-2">
                <Home size={16} />
                Retour à l'accueil
              </Link>
            </Button>
            
            {isRequestPage && (
              <Button asChild variant="outline" className="w-full">
                <Link to="/" className="flex items-center justify-center gap-2">
                  <Search size={16} />
                  Rechercher une autre demande
                </Link>
              </Button>
            )}
            
            <Button 
              variant="ghost" 
              className="w-full"
              onClick={() => window.history.back()}
            >
              <ArrowLeft size={16} className="mr-2" />
              Retour à la page précédente
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
