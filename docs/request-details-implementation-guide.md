# Request Details Page Implementation Guide

## Overview

This guide provides implementation details for transforming the current request details page into a more organized, visually appealing, and user-friendly interface.

## New Components Created

### 1. **RequestDetailsTabs** (`/request-details-v2/RequestDetailsTabs.tsx`)
- Main container with tabbed navigation
- 5 tabs: Overview, Documents, Analysis, Timeline, Chat
- Mobile-responsive with icon-only view on small screens
- Sticky tab navigation for better UX

### 2. **MetricsDashboard** (`/request-details-v2/MetricsDashboard.tsx`)
- Visual representation of key metrics
- Collapsible view for space efficiency
- Progress indicators and phase timeline
- Color-coded metric cards with trends

### 3. **EnhancedIndexCard** (`/request-details-v2/EnhancedIndexCard.tsx`)
- Redesigned index entry cards with visual completion indicators
- Collapsible actes preview section
- Metadata pills for quick information
- Action dropdown for document operations

### 4. **FilterBar** (`/request-details-v2/FilterBar.tsx`)
- Advanced search and filtering capabilities
- Type, status, and date range filters
- Grid/list view toggle
- Active filter indicators with clear option

### 5. **AnalysisSection** (`/request-details-v2/AnalysisSection.tsx`)
- Organized display of analysis data
- Priority-based categorization
- Expandable accordion sections
- Visual alerts for high-priority items

### 6. **DocumentRelationshipGraph** (`/request-details-v2/DocumentRelationshipGraph.tsx`)
- Interactive Mermaid diagram showing document hierarchy
- Visual flow from Request → Index → Actes
- Color-coded nodes based on status
- Zoom controls (50% to 200%)
- Fullscreen mode support
- Export to SVG functionality
- Legend for status indicators

## Implementation Steps

### Step 1: Update the Main RequestDetails Component

```typescript
// src/pages/RequestDetails.tsx
import RequestDetailsTabs from '@/components/request-details-v2/RequestDetailsTabs';
import MetricsDashboard from '@/components/request-details-v2/MetricsDashboard';
import AnalysisSection from '@/components/request-details-v2/AnalysisSection';
import FilterBar from '@/components/request-details-v2/FilterBar';
import EnhancedIndexCard from '@/components/request-details-v2/EnhancedIndexCard';

const RequestDetails: React.FC = () => {
  // ... existing state and hooks

  // Calculate metrics
  const metrics = {
    totalIndex: indexEntries.length,
    completedIndex: indexEntries.filter(i => i.is_completed).length,
    totalActes: Object.values(actesByIndex).flat().length,
    completedActes: Object.values(actesByIndex).flat().filter(a => a.document_completed).length,
    currentPhase: request?.status?.includes('Phase') ? request.status.split(' ')[1] : '1',
    daysSinceCreation: Math.floor((Date.now() - new Date(request?.created_at).getTime()) / (1000 * 60 * 60 * 24))
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <main className="flex-1 container max-w-screen-xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Back button */}
        <div className="mb-6">
          <Link to="/" className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Retour au tableau de bord
          </Link>
        </div>

        {/* Summary header */}
        <RequestDetailsSummary request={request} indexCount={indexEntries.length} acteCount={Object.values(actesByIndex).flat().length} />
        
        {/* Metrics Dashboard */}
        <div className="mt-6">
          <MetricsDashboard metrics={metrics} />
        </div>

        {/* Tabbed Content */}
        <div className="mt-8">
          <RequestDetailsTabs requestId={id}>
            {{
              overview: <OverviewTab request={request} />,
              documents: <DocumentsTab indexEntries={indexEntries} actesByIndex={actesByIndex} />,
              analysis: <AnalysisSection {...request} />,
              timeline: <TimelineTab requestId={id} indexEntries={indexEntries} actesByIndex={actesByIndex} />,
              chat: <RequestChatInterface requestId={id} />
            }}
          </RequestDetailsTabs>
        </div>
      </main>
    </div>
  );
};
```

### Step 2: Create Tab Content Components

#### Overview Tab
```typescript
const OverviewTab: React.FC<{ request: any }> = ({ request }) => {
  return (
    <div className="space-y-6">
      {/* Property Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de la propriété</CardTitle>
        </CardHeader>
        <CardContent>
          <RequestDetailsHeader request={request} />
        </CardContent>
      </Card>

      {/* Research Summary */}
      {request.id && <RequestResearchSummary requestId={request.id} />}
    </div>
  );
};
```

#### Documents Tab
```typescript
const DocumentsTab: React.FC<{ indexEntries: any[], actesByIndex: any }> = ({ 
  indexEntries, 
  actesByIndex 
}) => {
  const [view, setView] = useState<'grid' | 'list'>('grid');
  const [filteredEntries, setFilteredEntries] = useState(indexEntries);

  return (
    <div className="space-y-6">
      <FilterBar
        onSearch={(query) => {/* implement search */}}
        onFilterChange={(filters) => {/* implement filtering */}}
        onSortChange={(sort) => {/* implement sorting */}}
        onViewChange={setView}
        currentView={view}
        totalItems={indexEntries.length}
        filteredItems={filteredEntries.length}
      />

      <div className={cn(
        view === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 gap-4' 
          : 'space-y-4'
      )}>
        {filteredEntries.map((index, idx) => (
          <EnhancedIndexCard
            key={index.id}
            index={index}
            indexNumber={idx + 1}
            actesPreviews={/* format actes for preview */}
            onViewDocument={() => {/* handle view */}}
            onViewAllActes={() => {/* handle view all */}}
          />
        ))}
      </div>
    </div>
  );
};
```

### Step 3: State Management for Filters

Create a context for managing filter state across components:

```typescript
// src/contexts/RequestDetailsContext.tsx
interface RequestDetailsContextType {
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  sortOption: SortOption;
  setSortOption: (sort: SortOption) => void;
}

export const RequestDetailsContext = createContext<RequestDetailsContextType>();
```

### Step 4: Progressive Enhancement

1. **Lazy Loading**: Load tab content only when accessed
2. **Virtualization**: For large lists of documents
3. **Skeleton Loading**: Show loading states for better UX
4. **Error Boundaries**: Graceful error handling

### Step 5: Mobile Optimizations

1. **Touch Gestures**: Swipeable tabs on mobile
2. **Bottom Sheets**: For filters on mobile
3. **Responsive Typography**: Adjust font sizes
4. **Simplified Layouts**: Stack elements vertically

## Migration Strategy

### Phase 1: Parallel Implementation (Week 1)
- Create new components alongside existing ones
- Add feature flag to toggle between old/new views
- Test thoroughly with sample data

### Phase 2: Gradual Rollout (Week 2)
- Enable new view for internal testing
- Gather feedback and iterate
- Fix any integration issues

### Phase 3: Full Migration (Week 3)
- Switch all users to new view
- Deprecate old components
- Monitor performance and user feedback

### Phase 4: Cleanup (Week 4)
- Remove old components
- Optimize bundle size
- Document new architecture

## Performance Considerations

1. **Code Splitting**: Lazy load heavy components
2. **Memoization**: Use React.memo for expensive renders
3. **Debouncing**: For search and filter inputs
4. **Caching**: Cache API responses with React Query
5. **Image Optimization**: Lazy load document previews

## Testing Checklist

- [ ] All tabs load correctly
- [ ] Filters work as expected
- [ ] Search functionality is responsive
- [ ] Mobile view is properly formatted
- [ ] Accessibility standards are met
- [ ] Performance metrics are acceptable
- [ ] Error states are handled gracefully
- [ ] Data updates in real-time

## Accessibility Checklist

- [ ] Keyboard navigation works
- [ ] Screen readers can navigate tabs
- [ ] Color contrast meets WCAG standards
- [ ] Focus indicators are visible
- [ ] ARIA labels are descriptive
- [ ] Interactive elements have proper roles

## Future Enhancements

1. **Document Preview**: Inline PDF viewer
2. **Batch Operations**: Select multiple documents
3. **Export Options**: PDF, Excel, CSV formats
4. **Advanced Analytics**: Charts and graphs
5. **Collaboration**: Comments and annotations
6. **Version History**: Track document changes
