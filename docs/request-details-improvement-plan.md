# Request Details Page Improvement Plan

## Current Issues
1. **Information Overload**: Too much text displayed at once
2. **Poor Visual Hierarchy**: Difficult to scan and find relevant information
3. **Lack of Progressive Disclosure**: All details shown immediately
4. **Missing Visual Aids**: No charts, icons, or visual indicators
5. **Poor Mobile Experience**: Not optimized for smaller screens

## Proposed Information Architecture

### 1. **Header Section** (Always Visible)
- Request ID, Status Badge, Creation Date
- Quick Actions: Download PDF, View Folder, Share
- Progress Indicator (visual timeline of phases)

### 2. **Key Metrics Dashboard** (Collapsible)
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Total Index │ Completed   │ Total Actes │ Processing  │
│     12      │    10       │     45      │   Phase 3   │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### 3. **Tabbed Content Structure**

#### Tab 1: Overview
- **Property Information Card**
  - Seller details with icon
  - Property address with map preview
  - Search parameters (years, include radiated)
  
- **Research Summary** (Expandable)
  - Step-by-step timeline view
  - Collapsible sections for each phase
  - Visual progress indicators

#### Tab 2: Documents
- **Filter Bar**
  - Search by document number/name
  - Filter by type (Index/Acte)
  - Filter by status
  - Sort options

- **Document Grid/List View Toggle**
  - Card view with preview thumbnails
  - List view with sortable columns
  - Quick actions (View, Download, Details)

#### Tab 3: Analysis
- **Key Findings Summary**
  - Visual alerts for important items
  - Categorized by: Servitudes, Régimes, Erreurs, Autres
  - Priority indicators (High/Medium/Low)

- **Detailed Analysis** (Accordion)
  - Each category expandable
  - Markdown content with better styling
  - Related documents linked

#### Tab 4: Timeline
- **Visual Timeline**
  - Chronological view of all events
  - Filter by event type
  - Expandable details for each event

- **Document Relationship Graph**
  - Interactive Mermaid diagram showing document hierarchy
  - Visual representation of Index → Actes relationships
  - Color-coded status indicators
  - Zoom, pan, and fullscreen capabilities
  - Export to SVG functionality

#### Tab 5: Chat & Notes
- **AI Assistant Interface**
- **Manual Notes Section**
- **Activity Log**

### 4. **Enhanced Components**

#### A. Index Entry Card
```typescript
interface EnhancedIndexCard {
  // Visual header with status indicator
  header: {
    title: string; // "Index 1 - Lot 123"
    status: StatusBadge;
    completionPercentage: CircularProgress;
  };
  
  // Collapsible metadata
  metadata: {
    cadastre: string;
    docNumber: string;
    createdAt: string;
  };
  
  // Actes summary (collapsed by default)
  actesSummary: {
    total: number;
    completed: number;
    preview: ActePreview[]; // First 3 actes
  };
  
  // Actions
  actions: {
    viewDocument: () => void;
    viewAllActes: () => void;
    downloadPDF: () => void;
  };
}
```

#### B. Acte Entry Card
```typescript
interface EnhancedActeCard {
  // Compact header
  header: {
    publicationNumber: string;
    nature: string;
    relevanceScore: VisualRating; // Stars or bars
  };
  
  // Key information pills
  keyInfo: {
    parties: string[]; // Show first 2, "+X more"
    isRadiated: boolean;
    documentAvailable: boolean;
  };
  
  // Expandable details
  details: {
    summary: string; // Truncated with "Read more"
    fullDetails: string; // Shown on expand
  };
}
```

### 5. **Visual Enhancements**

#### A. Status Indicators
- Color-coded badges with icons
- Progress bars for completion status
- Visual timeline for phases

#### B. Data Visualization
- Pie chart for document distribution
- Timeline chart for processing phases
- Relevance heatmap for documents

#### C. Interactive Elements
- Hover tooltips for additional info
- Click-to-expand sections
- Smooth transitions and animations

### 6. **Performance Optimizations**

#### A. Lazy Loading
- Load tab content only when accessed
- Virtualized lists for large datasets
- Progressive image loading

#### B. Data Management
- Client-side filtering and sorting
- Debounced search inputs
- Memoized computed values

### 7. **Mobile Responsive Design**

#### A. Mobile Layout
- Stacked cards instead of grid
- Bottom sheet for filters
- Swipeable tabs
- Touch-optimized interactions

#### B. Progressive Enhancement
- Essential info prioritized
- Secondary details in expandable sections
- Optimized font sizes and spacing

### 8. **Accessibility Features**
- ARIA labels for all interactive elements
- Keyboard navigation support
- Screen reader optimized content
- High contrast mode support

## Implementation Priority

### Phase 1: Core Structure (Week 1)
1. Implement tabbed navigation
2. Create collapsible sections
3. Add search/filter functionality
4. Improve mobile responsiveness

### Phase 2: Visual Enhancements (Week 2)
1. Add progress indicators
2. Implement status badges
3. Create data visualizations
4. Add loading states

### Phase 3: Advanced Features (Week 3)
1. Document preview functionality
2. Export/sharing capabilities
3. Batch operations
4. Advanced filtering

### Phase 4: Polish & Optimization (Week 4)
1. Performance optimization
2. Accessibility audit
3. User testing
4. Final refinements

## Technical Implementation

### New Components Needed
1. `RequestDetailsTabs.tsx` - Main tabbed container
2. `DocumentsGrid.tsx` - Grid/list view for documents
3. `TimelineView.tsx` - Visual timeline component
4. `MetricsDashboard.tsx` - Key metrics display
5. `DocumentPreview.tsx` - Document viewer modal
6. `FilterBar.tsx` - Advanced filtering component
7. `DocumentRelationshipGraph.tsx` - Mermaid diagram for document relationships

### State Management
- Use React Context for filter/sort state
- Implement proper memoization
- Consider React Query for data fetching

### Styling Approach
- Tailwind CSS for consistent styling
- CSS modules for component-specific styles
- Framer Motion for animations
